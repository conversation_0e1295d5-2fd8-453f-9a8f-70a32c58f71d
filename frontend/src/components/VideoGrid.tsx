import React from 'react';
import { Video } from '../types';
import VideoCard from './VideoCard';
import LoadingSpinner from './LoadingSpinner';

interface VideoGridProps {
  videos: Video[];
  onLoadMore?: () => void;
  hasMore?: boolean;
  loading?: boolean;
}

const VideoGrid: React.FC<VideoGridProps> = ({
  videos,
  onLoadMore,
  hasMore = false,
  loading = false,
}) => {
  return (
    <div className="p-6">
      {/* Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {videos.map((video) => (
          <VideoCard key={video.id} video={video} />
        ))}
      </div>

      {/* Load More Button */}
      {hasMore && (
        <div className="mt-8 text-center">
          <button
            onClick={onLoadMore}
            disabled={loading}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Loading...
              </>
            ) : (
              'Load More Videos'
            )}
          </button>
        </div>
      )}

      {/* Loading indicator for pagination */}
      {loading && videos.length > 0 && (
        <div className="mt-8 text-center">
          <LoadingSpinner size="md" />
        </div>
      )}
    </div>
  );
};

export default VideoGrid;
