FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy all source code
COPY . .

# Debug: Show what was copied
RUN echo "=== DEBUG: Files in /app ===" && \
    ls -la && \
    echo "=== DEBUG: Looking for public directory ===" && \
    find . -name "public" -type d && \
    echo "=== DEBUG: Looking for index.html ===" && \
    find . -name "index.html" && \
    echo "=== DEBUG: Contents of public directory (if exists) ===" && \
    ls -la public/ 2>/dev/null || echo "Public directory not found!"

# If public directory doesn't exist, create it with a basic index.html
RUN if [ ! -d "public" ]; then \
        echo "Creating missing public directory..." && \
        mkdir -p public && \
        echo '<!DOCTYPE html><html><head><title>tagTok</title></head><body><div id="root"></div></body></html>' > public/index.html && \
        echo "Created basic index.html"; \
    fi

# Final verification
RUN echo "=== FINAL CHECK ===" && \
    ls -la public/ && \
    cat public/index.html

# Expose port
EXPOSE 3000

# Start development server
CMD ["npm", "start"]
