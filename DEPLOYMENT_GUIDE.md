# tagTok Deployment Quick Guide

## 🚀 Quick Deployment Checklist

### For Local Network Access (Home/Office)

**✅ Prerequisites:**
- [ ] Docker and Docker Compose installed
- [ ] 4GB+ RAM available
- [ ] 10GB+ free disk space

**✅ Setup Steps:**

1. **Get the code:**
   ```bash
   git clone https://github.com/your-username/tagTok-v2.git
   cd tagTok-v2
   ```

2. **Find your computer's IP address:**
   ```bash
   # Linux/macOS:
   ip addr show | grep "inet " | grep -v 127.0.0.1
   
   # Windows:
   ipconfig | findstr "IPv4"
   ```
   📝 **Your IP**: `192.168.x.x` (write it down)

3. **Deploy:**
   ```bash
   docker-compose up -d --build
   ```

4. **Test access:**
   - **From host computer**: http://localhost
   - **From other devices**: http://YOUR_IP (e.g., http://*************)
   - **Mobile devices**: Same URL, fully responsive!

**✅ Verification:**
- [ ] Can access from host computer
- [ ] Can access from another computer on network
- [ ] Can access from mobile phone
- [ ] Videos upload and process correctly
- [ ] API health check works: `curl http://YOUR_IP/api/health`

---

### For Remote Server Deployment

**✅ Server Requirements:**
- [ ] Ubuntu/Debian server with root access
- [ ] 4GB+ RAM, 20GB+ storage
- [ ] Public IP address
- [ ] Domain name (optional)

**✅ Quick Server Setup:**

1. **Install Docker:**
   ```bash
   sudo apt update && sudo apt upgrade -y
   sudo apt install docker.io docker-compose git -y
   sudo usermod -aG docker $USER
   sudo systemctl enable docker
   ```

2. **Configure firewall:**
   ```bash
   sudo ufw allow 22    # SSH
   sudo ufw allow 80    # HTTP
   sudo ufw enable
   ```

3. **Deploy application:**
   ```bash
   git clone https://github.com/your-username/tagTok-v2.git
   cd tagTok-v2
   docker-compose up -d --build
   ```

4. **Access:**
   - **Public access**: http://YOUR_SERVER_IP
   - **With domain**: http://yourdomain.com

---

## 🔧 Common Configuration Changes

### Change Default Ports
```yaml
# In docker-compose.yml:
services:
  nginx:
    ports:
      - "8080:80"  # Change from port 80 to 8080
```

### Reduce Memory Usage
```python
# In backend/utils/ai_utils.py:
self.model_size = "tiny"  # Change from "base" to "tiny"
```

### Enable HTTPS (Production)
```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d yourdomain.com
```

---

## 🐛 Troubleshooting

### "Failed to load videos" on mobile
✅ **This is automatically fixed!** The app detects IP access and uses the correct API endpoint.

### Cannot access from other devices
```bash
# Check if services are running
docker-compose ps

# Test local access first
curl http://localhost/api/health

# Check firewall (Linux)
sudo ufw status

# Check what's using port 80
sudo netstat -tlnp | grep :80
```

### Slow video processing
```bash
# Use smaller AI model
# Edit backend/utils/ai_utils.py and change:
self.model_size = "tiny"  # Fastest processing

# Restart backend
docker-compose restart backend
```

### Out of disk space
```bash
# Check disk usage
df -h

# Clean up Docker
docker system prune -a

# Move data directory to larger drive
mv data /path/to/larger/drive/
ln -s /path/to/larger/drive/data data
```

---

## 📱 Mobile Access Features

When accessing from mobile devices, tagTok automatically provides:

- ✅ **Responsive Design**: Optimized for phone screens
- ✅ **Touch-Friendly**: Large buttons and touch targets
- ✅ **Auto API Detection**: Automatically uses correct server IP
- ✅ **Mobile Upload**: Drag & drop or file picker
- ✅ **Mobile Video Player**: Touch controls and gestures
- ✅ **Offline-First**: Works without internet after initial load

---

## 🔒 Security Notes

### For Local Network (Home/Office)
- ✅ **Safe**: Only accessible on your local network
- ✅ **No Internet Exposure**: Cannot be accessed from outside
- ✅ **Private Data**: All data stays on your local machine

### For Public Server
- ⚠️ **Consider HTTPS**: Use SSL certificates for production
- ⚠️ **Change Default Ports**: Use non-standard ports
- ⚠️ **Regular Backups**: Backup your data directory
- ⚠️ **Monitor Access**: Check logs regularly

---

## 📞 Quick Support

**Check logs:**
```bash
docker-compose logs -f backend
docker-compose logs -f frontend
```

**Restart services:**
```bash
docker-compose restart
```

**Complete reset:**
```bash
docker-compose down
docker-compose up -d --build
```

**Health check:**
```bash
curl http://YOUR_IP/api/health
```

---

**🎉 That's it! Your tagTok instance should now be accessible from any device on your network!**

For more detailed information, see the full deployment guide in README.md.
