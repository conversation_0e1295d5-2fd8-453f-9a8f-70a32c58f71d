import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import tempfile
import os

from main import app
from models.database import get_db, Base

# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture(scope="module")
def client():
    # Create test database tables
    Base.metadata.create_all(bind=engine)
    with TestClient(app) as c:
        yield c
    # Clean up
    Base.metadata.drop_all(bind=engine)
    if os.path.exists("test.db"):
        os.remove("test.db")

def test_health_check(client):
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert data["version"] == "1.0.0"

def test_get_videos_empty(client):
    """Test getting videos when none exist"""
    response = client.get("/videos")
    assert response.status_code == 200
    assert response.json() == []

def test_get_tags_empty(client):
    """Test getting tags when none exist"""
    response = client.get("/tags")
    assert response.status_code == 200
    assert response.json() == []

def test_analytics_empty(client):
    """Test analytics with no data"""
    response = client.get("/analytics")
    assert response.status_code == 200
    data = response.json()
    assert data["total_videos"] == 0
    assert data["total_tags"] == 0
    assert data["processed_videos"] == 0

def test_create_tag(client):
    """Test creating a new tag"""
    tag_data = {
        "name": "Test Tag",
        "color": "#FF0000",
        "description": "A test tag"
    }
    response = client.post("/tags", json=tag_data)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == tag_data["name"]
    assert data["color"] == tag_data["color"]
    assert data["description"] == tag_data["description"]
    assert data["usage_count"] == 0

def test_create_duplicate_tag(client):
    """Test creating a tag with duplicate name"""
    tag_data = {
        "name": "Test Tag",
        "color": "#00FF00",
        "description": "Another test tag"
    }
    response = client.post("/tags", json=tag_data)
    assert response.status_code == 400
    assert "already exists" in response.json()["detail"]

def test_get_tags_with_data(client):
    """Test getting tags when data exists"""
    response = client.get("/tags")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["name"] == "Test Tag"

def test_invalid_tag_color(client):
    """Test creating tag with invalid color"""
    tag_data = {
        "name": "Invalid Color Tag",
        "color": "not-a-color",
        "description": "Invalid color"
    }
    response = client.post("/tags", json=tag_data)
    assert response.status_code == 422  # Validation error

def test_upload_no_files(client):
    """Test upload endpoint with no files"""
    response = client.post("/videos/upload", files=[])
    assert response.status_code == 422  # Validation error

def test_get_nonexistent_video(client):
    """Test getting a video that doesn't exist"""
    response = client.get("/videos/999")
    assert response.status_code == 404

def test_get_nonexistent_tag(client):
    """Test getting a tag that doesn't exist"""
    response = client.get("/tags/999")
    assert response.status_code == 404

def test_export_empty_data(client):
    """Test exporting when no data exists"""
    response = client.get("/export/videos?format=json")
    assert response.status_code == 200
    # Should return empty data but valid response

def test_tag_cloud_empty(client):
    """Test tag cloud with no data"""
    response = client.get("/tags/cloud/data")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) == 0  # No tags with usage > 0
