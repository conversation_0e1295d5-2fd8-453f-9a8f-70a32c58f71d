import asyncio
import os
import subprocess
import tempfile
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
import random

from models.database import Video, Tag, ProcessingJob
from datetime import datetime
from services.video_service import VideoService
from services.tag_service import TagService
from utils.ai_utils import WhisperTranscriber, TagGenerator
from utils.video_utils import extract_video_frames

class ProcessingService:
    def __init__(self, db: Session):
        self.db = db
        self.video_service = VideoService(db)
        self.tag_service = TagService(db)
        self.transcriber = WhisperTranscriber()
        self.tag_generator = TagGenerator()
    
    async def process_video_async(self, video_id: int):
        """Process a video asynchronously (transcription + tagging)"""
        try:
            # Update status to processing with 0% progress
            self.video_service.update_processing_status(video_id, "processing", progress=0)

            # Create processing job
            job = ProcessingJob(
                video_id=video_id,
                job_type="full_processing",
                status="running",
                started_at=datetime.now()
            )
            self.db.add(job)
            self.db.commit()

            # Get video
            video = self.video_service.get_video_by_id(video_id)
            if not video:
                raise Exception(f"Video {video_id} not found")

            # Step 1: Transcribe video (0% -> 70%)
            self.video_service.update_processing_status(video_id, "processing", progress=10)
            print(f"Starting transcription for video {video_id}")

            transcript, language = await self.transcribe_video(video.file_path)

            self.video_service.update_processing_status(video_id, "processing", progress=70)
            print(f"Transcription completed for video {video_id}")

            # Step 2: Generate tags from transcript (70% -> 90%)
            self.video_service.update_processing_status(video_id, "processing", progress=75)
            print(f"Starting tag generation for video {video_id}")

            suggested_tags = await self.generate_tags_from_transcript(
                transcript, video.title or video.original_filename
            )

            self.video_service.update_processing_status(video_id, "processing", progress=85)
            print(f"Tag generation completed for video {video_id}")

            # Step 3: Save tags and finalize (85% -> 100%)
            self.video_service.update_processing_status(video_id, "processing", progress=90)
            
            # Step 3: Create and assign tags
            for tag_data in suggested_tags:
                # Check if tag exists
                existing_tag = self.tag_service.get_tag_by_name(tag_data["name"])
                if not existing_tag:
                    # Create new tag
                    from models.schemas import TagCreate
                    tag_create = TagCreate(
                        name=tag_data["name"],
                        color=tag_data["color"],
                        description=tag_data["description"]
                    )
                    tag = self.tag_service.create_tag(tag_create)
                else:
                    tag = existing_tag
                
                # Add tag to video
                self.tag_service.add_tag_to_video(tag.id, video_id)
            
            # Update video with transcript and status (100% complete)
            self.video_service.update_processing_status(
                video_id, "completed", transcript, language, progress=100
            )

            # Update job status
            job.status = "completed"
            job.completed_at = datetime.now()
            self.db.commit()

            print(f"Video {video_id} processing completed successfully")
            
        except Exception as e:
            print(f"Error processing video {video_id}: {e}")
            
            # Update status to failed
            self.video_service.update_processing_status(video_id, "failed")
            
            # Update job status
            if 'job' in locals():
                job.status = "failed"
                job.error_message = str(e)
                self.db.commit()
    
    async def transcribe_video(self, video_path: str) -> tuple[str, str]:
        """Transcribe video using Whisper"""
        try:
            transcript, language = await self.transcriber.transcribe(video_path)
            return transcript, language
        except Exception as e:
            print(f"Transcription failed for {video_path}: {e}")
            return "", "unknown"
    
    async def generate_tags_from_transcript(
        self, 
        transcript: str, 
        title: str = ""
    ) -> List[Dict[str, str]]:
        """Generate tags from transcript and title"""
        try:
            # Combine transcript and title for analysis
            text_content = f"{title}\n\n{transcript}" if title else transcript
            
            if not text_content.strip():
                return []
            
            # Generate tags using AI
            tags = await self.tag_generator.generate_tags(text_content)
            return tags
        except Exception as e:
            print(f"Tag generation failed: {e}")
            return []
    
    def reprocess_video(self, video_id: int):
        """Queue video for reprocessing"""
        # Reset processing status
        self.video_service.update_processing_status(video_id, "pending")
        
        # Clear existing tags (optional - you might want to keep them)
        video = self.video_service.get_video_by_id(video_id)
        if video:
            # Remove all tags from video
            for tag in video.tags[:]:  # Create a copy to avoid modification during iteration
                self.tag_service.remove_tag_from_video(tag.id, video_id)
        
        # Queue for processing
        asyncio.create_task(self.process_video_async(video_id))
    
    def get_processing_queue_status(self) -> Dict[str, Any]:
        """Get status of processing queue"""
        pending_jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.status == "pending"
        ).count()
        
        running_jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.status == "running"
        ).count()
        
        completed_jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.status == "completed"
        ).count()
        
        failed_jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.status == "failed"
        ).count()
        
        return {
            "pending": pending_jobs,
            "running": running_jobs,
            "completed": completed_jobs,
            "failed": failed_jobs,
            "total": pending_jobs + running_jobs + completed_jobs + failed_jobs
        }
    
    def cleanup_failed_jobs(self):
        """Clean up failed processing jobs"""
        failed_jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.status == "failed"
        ).all()
        
        for job in failed_jobs:
            # Reset video status to pending for retry
            self.video_service.update_processing_status(job.video_id, "pending")
            
            # Delete failed job
            self.db.delete(job)
        
        self.db.commit()
    
    def get_video_processing_history(self, video_id: int) -> List[ProcessingJob]:
        """Get processing history for a video"""
        return self.db.query(ProcessingJob).filter(
            ProcessingJob.video_id == video_id
        ).order_by(ProcessingJob.id.desc()).all()
